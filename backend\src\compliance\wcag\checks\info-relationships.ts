/**
 * WCAG Rule 3: Info and Relationships - 1.3.1
 * 90% Automated - Minimal manual review for semantic accuracy
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { ManualReviewItem } from '../utils/manual-review-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { AdvancedLayoutAnalyzer } from '../utils/advanced-layout-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { AdvancedPatternDetector } from '../utils/advanced-pattern-detector';
import { PatternRecognitionEngine } from '../utils/pattern-recognition-engine';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface InfoRelationshipsConfig extends EnhancedCheckConfig {
  enableSemanticValidation?: boolean;
  enablePatternValidation?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableAdvancedPatternDetection?: boolean;
  enablePatternRecognition?: boolean;
  checkHeadingStructure?: boolean;
  checkFormRelationships?: boolean;
  checkTableRelationships?: boolean;
}

export class InfoRelationshipsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private layoutAnalyzer = new LayoutAnalyzer();
  private advancedLayoutAnalyzer = AdvancedLayoutAnalyzer.getAdvancedInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getWideGamutInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  /**
   * Perform info and relationships check - 90% automated with enhanced evidence
   */
  async performCheck(config: InfoRelationshipsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: InfoRelationshipsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableSemanticValidation: true,
      enablePatternValidation: true,
      enableContentQualityAnalysis: true,
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableAdvancedPatternDetection: true,
      enablePatternRecognition: true,
      checkHeadingStructure: true,
      checkFormRelationships: true,
      checkTableRelationships: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-003',
      'Info and Relationships',
      'perceivable',
      0.09,
      'A',
      enhancedConfig,
      this.executeInfoRelationshipsCheck.bind(this),
      true, // Requires browser
      true, // Manual review for complex cases
    );

    // Enhanced evidence standardization with semantic structure analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-003',
        ruleName: 'Info and Relationships',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'semantic-structure-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          headingStructureAnalysis: true,
          formRelationshipAnalysis: true,
          tableRelationshipAnalysis: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
          phase3Enhancement: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 50,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive semantic structure analysis
   */
  private async executeInfoRelationshipsCheck(page: Page, config: InfoRelationshipsConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Phase 3 Enhancement: AI Semantic Validation
    if (config.enableAISemanticValidation) {
      try {
        const aiSemanticReport =
          await this.aiSemanticValidator.validateSemanticsWithAI(page);

        // Add AI semantic validation evidence
        evidence.push({
          type: 'info',
          description: 'AI-powered semantic structure validation',
          element: 'semantic-structure',
          value: JSON.stringify({
            aiConfidence: aiSemanticReport.aiConfidence,
            contentQuality: aiSemanticReport.contentQuality.readabilityScore,
            patternValidation: aiSemanticReport.patternValidation.overallScore,
            contextualAnalysis: aiSemanticReport.contextualAnalysis.pageContext.type,
            enhancedRecommendations: aiSemanticReport.enhancedRecommendations.slice(0, 3),
          }),
          severity: aiSemanticReport.aiConfidence > 80 ? 'info' : 'warning',
        });

        // Collect AI-enhanced recommendations
        if (aiSemanticReport.enhancedRecommendations.length > 0) {
          recommendations.push(...aiSemanticReport.enhancedRecommendations.slice(0, 5));
        }
      } catch (error) {
        console.warn('AI semantic validation failed, continuing with standard analysis:', error);
      }
    }

    // Phase 3 Enhancement: Content Quality Analysis
    if (config.enableContentQualityAnalysis) {
      try {
        const contentQualityReport = await this.contentQualityAnalyzer.analyzeContentQuality(page);

        // Add content quality evidence
        evidence.push({
          type: 'info',
          description: 'Content quality and semantic analysis',
          element: 'content-quality',
          value: JSON.stringify({
            overallQuality: contentQualityReport.summary.overallQuality,
            readabilityGrade: contentQualityReport.readability.averageGrade,
            accessibilityLevel: contentQualityReport.accessibility.level,
            vocabularyRichness: contentQualityReport.semantic.vocabularyAnalysis.vocabularyRichness,
          }),
          severity: contentQualityReport.summary.overallQuality >= 70 ? 'info' : 'warning',
        });
      } catch (error) {
        console.warn('Content quality analysis failed, continuing with standard analysis:', error);
      }
    }

    // Phase 3 Enhancement: Accessibility Pattern Analysis
    if (config.enableAccessibilityPatterns) {
      try {
        const patternReport = await this.accessibilityPatternLibrary.analyzePatterns(page);

        // Add accessibility pattern evidence
        evidence.push({
          type: 'info',
          description: 'Accessibility pattern library validation',
          element: 'accessibility-patterns',
          value: JSON.stringify({
            totalPatterns: patternReport.totalPatterns,
            validPatterns: patternReport.validPatterns,
            overallScore: patternReport.overallScore,
            criticalIssues: patternReport.criticalIssues.slice(0, 3),
          }),
          severity: patternReport.overallScore >= 80 ? 'info' : 'warning',
        });

        // Collect pattern-based recommendations
        if (patternReport.recommendations.length > 0) {
          recommendations.push(...patternReport.recommendations.slice(0, 3));
        }
      } catch (error) {
        console.warn(
          'Accessibility pattern analysis failed, continuing with standard analysis:',
          error,
        );
      }
    }

    // Milestone 5.3: Advanced Pattern Detection
    if (config.enableAdvancedPatternDetection) {
      try {
        const advancedPatternReport =
          await this.advancedPatternDetector.performAdvancedPatternDetection(page);

        // Add advanced pattern detection evidence
        evidence.push({
          type: 'info',
          description: 'Advanced pattern detection analysis',
          element: 'advanced-patterns',
          value: JSON.stringify({
            semanticPatterns: advancedPatternReport.semanticPatterns.length,
            behavioralPatterns: advancedPatternReport.behavioralPatterns.length,
            crossElementPatterns: advancedPatternReport.crossElementPatterns.length,
            mlClassificationResults: advancedPatternReport.mlClassificationResults.length,
            advancedMetrics: advancedPatternReport.advancedMetrics,
          }),
          severity:
            advancedPatternReport.advancedMetrics.semanticAccuracy > 0.8 ? 'info' : 'warning',
        });

        // Collect advanced pattern recommendations
        if (advancedPatternReport.recommendations.length > 0) {
          recommendations.push(...advancedPatternReport.recommendations.slice(0, 3));
        }
      } catch (error) {
        console.warn(
          'Advanced pattern detection failed, continuing with standard analysis:',
          error,
        );
      }
    }

    // Milestone 5.3: Pattern Recognition Engine
    if (config.enablePatternRecognition) {
      try {
        const recognitionReport = await this.patternRecognitionEngine.recognizePatterns(page);

        // Add pattern recognition evidence
        evidence.push({
          type: 'info',
          description: 'Pattern recognition engine analysis',
          element: 'pattern-recognition',
          value: JSON.stringify({
            totalPatterns: recognitionReport.totalPatterns,
            patternCategories: recognitionReport.patternCategories,
            complianceBreakdown: recognitionReport.complianceBreakdown,
            overallScore: recognitionReport.overallScore,
            analysisMetrics: recognitionReport.analysisMetrics,
          }),
          severity: recognitionReport.overallScore >= 80 ? 'info' : 'warning',
        });

        // Collect critical issues from pattern recognition
        if (recognitionReport.criticalIssues.length > 0) {
          issues.push(...recognitionReport.criticalIssues.slice(0, 3));
        }

        // Collect pattern recognition recommendations
        if (recognitionReport.recommendations.length > 0) {
          recommendations.push(...recognitionReport.recommendations.slice(0, 3));
        }
      } catch (error) {
        console.warn('Pattern recognition failed, continuing with standard analysis:', error);
      }
    }

    // Enhanced info and relationships analysis using AdvancedLayoutAnalyzer
    try {
      const structureAnalysis = await this.advancedLayoutAnalyzer.analyzeResponsiveLayout(page);

      // Add enhanced evidence from advanced semantic structure analysis
      evidence.push({
        type: 'info',
        description: 'Advanced semantic structure and relationship analysis',
        element: 'semantic-elements',
        value: JSON.stringify({
          overallScore: structureAnalysis.overallScore,
          criticalIssues: structureAnalysis.criticalIssues,
          recommendations: structureAnalysis.recommendations,
          performanceMetrics: structureAnalysis.performanceMetrics,
        }),
        severity: structureAnalysis.criticalIssues.length > 0 ? 'error' : 'info',
      });

      // Collect issues and recommendations from advanced analysis
      if (structureAnalysis.criticalIssues.length > 0) {
        issues.push(...structureAnalysis.criticalIssues);
        recommendations.push(...structureAnalysis.recommendations);
      }
    } catch (error) {
      console.warn(
        'Advanced semantic structure analysis failed, falling back to basic analysis:',
        error,
      );
    }

    // Analyze heading structure - Basic fallback analysis
    const headingAnalysis = await this.analyzeHeadingStructure(page);

    // Analyze form relationships
    const formAnalysis = await this.analyzeFormRelationships(page);

    // Analyze table relationships
    const tableAnalysis = await this.analyzeTableRelationships(page);

    // Analyze list structures
    const listAnalysis = await this.analyzeListStructures(page);

    // Analyze ARIA relationships
    const ariaAnalysis = await this.analyzeAriaRelationships(page);

    // Combine all analyses
    const allAnalyses = [headingAnalysis, formAnalysis, tableAnalysis, listAnalysis, ariaAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore =
      automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Info and relationships analysis summary',
      value: `${passedChecks}/${automatedChecks} relationships pass automated checks, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error',
    });

    return {
      score: automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze heading structure and hierarchy
   */
  private async analyzeHeadingStructure(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
      let totalChecks = 0;
      let passedChecks = 0;

      if (headings.length === 0) {
        issues.push('No headings found on page');
        recommendations.push('Add proper heading structure to organize content');
        return {
          totalChecks: 1,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      // Check for h1
      totalChecks++;
      const h1Elements = headings.filter((h) => h.tagName === 'H1');
      if (h1Elements.length === 1) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'Page has exactly one H1 element',
          value: `H1: "${h1Elements[0].textContent?.trim()}"`,
          severity: 'info',
        });
      } else if (h1Elements.length === 0) {
        issues.push('Page missing H1 element');
        recommendations.push('Add an H1 element to identify the main page topic');
      } else {
        issues.push(`Page has ${h1Elements.length} H1 elements (should have exactly 1)`);
        recommendations.push('Use only one H1 element per page');
      }

      // Check heading hierarchy
      totalChecks++;
      let hierarchyValid = true;
      let previousLevel = 0;

      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));

        if (index === 0 && level !== 1) {
          hierarchyValid = false;
          issues.push('First heading is not H1');
        } else if (level > previousLevel + 1) {
          hierarchyValid = false;
          issues.push(`Heading level skipped: ${heading.tagName} follows H${previousLevel}`);
        }

        previousLevel = level;
      });

      if (hierarchyValid) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'Heading hierarchy is logical',
          value: `${headings.length} headings follow proper nesting`,
          severity: 'info',
        });
      } else {
        recommendations.push('Ensure headings follow logical hierarchy (H1 > H2 > H3, etc.)');
      }

      // Check for empty headings
      totalChecks++;
      const emptyHeadings = headings.filter((h) => !h.textContent?.trim());
      if (emptyHeadings.length === 0) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'All headings have text content',
          value: 'No empty headings found',
          severity: 'info',
        });
      } else {
        issues.push(`${emptyHeadings.length} empty headings found`);
        recommendations.push('Provide descriptive text for all headings');
      }

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze form label relationships
   */
  private async analyzeFormRelationships(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      function generateSelector(element: Element, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function hasProperLabel(input: HTMLInputElement): {
        isLabeled: boolean;
        labelText: string;
        reason: string;
      } {
        // Check for explicit label
        if (input.id) {
          const label = document.querySelector(`label[for="${input.id}"]`);
          if (label && label.textContent?.trim()) {
            return {
              isLabeled: true,
              labelText: label.textContent.trim(),
              reason: 'Has explicit label',
            };
          }
        }

        // Check for implicit label (input inside label)
        const parentLabel = input.closest('label');
        if (parentLabel && parentLabel.textContent?.trim()) {
          return {
            isLabeled: true,
            labelText: parentLabel.textContent.trim(),
            reason: 'Has implicit label',
          };
        }

        // Check for aria-label
        const ariaLabel = input.getAttribute('aria-label');
        if (ariaLabel && ariaLabel.trim()) {
          return {
            isLabeled: true,
            labelText: ariaLabel.trim(),
            reason: 'Has aria-label',
          };
        }

        // Check for aria-labelledby
        const ariaLabelledby = input.getAttribute('aria-labelledby');
        if (ariaLabelledby) {
          const referencedElement = document.getElementById(ariaLabelledby);
          if (referencedElement && referencedElement.textContent?.trim()) {
            return {
              isLabeled: true,
              labelText: referencedElement.textContent.trim(),
              reason: 'Has aria-labelledby',
            };
          }
        }

        return {
          isLabeled: false,
          labelText: '',
          reason: 'No accessible label found',
        };
      }

      const formControls = Array.from(
        document.querySelectorAll('input:not([type="hidden"]), select, textarea'),
      );
      const totalChecks = formControls.length;
      let passedChecks = 0;

      if (formControls.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      formControls.forEach((control, index) => {
        const input = control as HTMLInputElement;
        const selector = generateSelector(input, index);

        // Check for label association
        const labelCheck = hasProperLabel(input);

        if (labelCheck.isLabeled) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Form control has proper label',
            value: `Label: "${labelCheck.labelText}"`,
            selector,
            severity: 'info',
          });
        } else {
          issues.push(`Form control missing label: ${selector}`);
          recommendations.push(`Add proper label for ${selector}`);
          evidence.push({
            type: 'text',
            description: 'Form control missing proper label',
            value: labelCheck.reason,
            selector,
            severity: 'error',
          });
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze table relationships
   */
  private async analyzeTableRelationships(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      const tables = Array.from(document.querySelectorAll('table'));
      let totalChecks = 0;
      let passedChecks = 0;

      if (tables.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      tables.forEach((table, index) => {
        const selector = `table:nth-of-type(${index + 1})`;

        // Check for table headers
        totalChecks++;
        const headers = table.querySelectorAll('th');
        if (headers.length > 0) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Table has header cells',
            value: `${headers.length} header cells found`,
            selector,
            severity: 'info',
          });
        } else {
          issues.push(`Table missing header cells: ${selector}`);
          recommendations.push(`Add <th> elements to ${selector}`);
        }

        // Check for table caption
        totalChecks++;
        const caption = table.querySelector('caption');
        if (caption && caption.textContent?.trim()) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Table has descriptive caption',
            value: `Caption: "${caption.textContent.trim()}"`,
            selector,
            severity: 'info',
          });
        } else {
          // Caption is recommended but not always required - add as manual review
          manualReviewItems.push({
            selector,
            description: 'Table caption verification needed',
            automatedFindings: 'No caption found',
            reviewRequired: 'Verify if table needs a caption to describe its purpose',
            priority: 'medium',
            estimatedTime: 2,
          });
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze list structures
   */
  private async analyzeListStructures(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      const lists = Array.from(document.querySelectorAll('ul, ol, dl'));
      let totalChecks = 0;
      let passedChecks = 0;

      if (lists.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      lists.forEach((list, index) => {
        const selector = `${list.tagName.toLowerCase()}:nth-of-type(${index + 1})`;

        totalChecks++;

        if (list.tagName === 'DL') {
          // Definition list - check for dt/dd pairs
          const terms = list.querySelectorAll('dt');
          const definitions = list.querySelectorAll('dd');

          if (terms.length > 0 && definitions.length > 0) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Definition list has proper structure',
              value: `${terms.length} terms, ${definitions.length} definitions`,
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Definition list missing terms or definitions: ${selector}`);
            recommendations.push(`Add proper dt/dd structure to ${selector}`);
          }
        } else {
          // Ordered/unordered list - check for list items
          const items = list.querySelectorAll('li');

          if (items.length > 0) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'List has proper structure',
              value: `${items.length} list items`,
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`List missing list items: ${selector}`);
            recommendations.push(`Add <li> elements to ${selector}`);
          }
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze ARIA relationships
   */
  private async analyzeAriaRelationships(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      const ariaElements = Array.from(
        document.querySelectorAll(
          '[aria-labelledby], [aria-describedby], [aria-controls], [aria-owns]',
        ),
      );
      const totalChecks = ariaElements.length;
      let passedChecks = 0;

      if (ariaElements.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      ariaElements.forEach((element, index) => {
        const selector = element.id
          ? `#${element.id}`
          : `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;

        // Check aria-labelledby references
        const labelledby = element.getAttribute('aria-labelledby');
        if (labelledby) {
          const referencedElement = document.getElementById(labelledby);
          if (referencedElement) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'ARIA labelledby reference is valid',
              value: `References: #${labelledby}`,
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Invalid aria-labelledby reference: ${selector} -> #${labelledby}`);
            recommendations.push(`Fix aria-labelledby reference in ${selector}`);
          }
        }

        // Check aria-describedby references
        const describedby = element.getAttribute('aria-describedby');
        if (describedby) {
          const referencedElement = document.getElementById(describedby);
          if (referencedElement) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'ARIA describedby reference is valid',
              value: `References: #${describedby}`,
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Invalid aria-describedby reference: ${selector} -> #${describedby}`);
            recommendations.push(`Fix aria-describedby reference in ${selector}`);
          }
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }
}
